# 增强表格处理功能

## 概述

本文档描述了HTML到Markdown转换器中增强的表格处理功能，特别是对常用前端框架的兼容性支持。

## 支持的框架

### 1. Bootstrap
- **支持的CSS类**: `table`, `table-striped`, `table-bordered`, `table-hover`, `table-condensed`, `table-responsive`, `table-dark`, `table-light`, `table-sm`, `table-lg`
- **特殊处理**: 
  - 自动识别Bootstrap表格结构
  - 处理`.badge`组件，提取文本内容
  - 支持响应式表格包装器

### 2. Tailwind CSS
- **支持的CSS类**: `table-auto`, `table-fixed`, `border-collapse`, `border-separate`, `table-caption`, `table-cell`, `table-column`, `table-column-group`, `table-footer-group`, `table-header-group`, `table-row`, `table-row-group`
- **特殊处理**:
  - 识别Tailwind表格样式
  - 处理响应式包装器如`overflow-x-auto`

### 3. jQuery DataTables
- **支持的CSS类**: `dataTable`, `dataTables_wrapper`, `dataTables_length`, `dataTables_filter`, `dataTables_info`, `dataTables_paginate`, `display`, `cell-border`, `stripe`
- **特殊处理**:
  - 忽略排序控件（`.sorting`, `.sorting_asc`, `.sorting_desc`）
  - 移除DataTables内部元素（`.dataTables_sizing`）
  - 智能提取表格数据

### 4. Material Design
- **支持的CSS类**: `mdl-data-table`, `mdc-data-table`, `mat-table`, `material-table`
- **特殊处理**:
  - 支持Material Design组件选择器
  - 处理`.mdc-data-table__cell`和`.mat-cell`
  - 提取`.mdc-data-table__cell-content`内容

### 5. Foundation
- **支持的CSS类**: `table`, `stack`, `scroll`, `hover`, `unstriped`
- **特殊处理**:
  - 识别Foundation表格样式
  - 保持表格结构完整性

### 6. Semantic UI
- **支持的CSS类**: `ui`, `table`, `celled`, `striped`, `selectable`, `inverted`, `definition`
- **特殊处理**:
  - 识别Semantic UI表格组件
  - 处理状态类如`.positive`, `.negative`

### 7. Ant Design
- **支持的CSS类**: `ant-table`, `ant-table-wrapper`, `ant-table-container`, `ant-table-content`, `ant-table-thead`, `ant-table-tbody`, `ant-table-tfoot`, `ant-table-cell`, `ant-table-row`, `ant-table-header`, `ant-table-body`, `ant-table-footer`, `ant-table-bordered`, `ant-table-striped`, `ant-table-hover`, `ant-table-small`, `ant-table-middle`, `ant-table-large`, `ant-table-fixed-header`, `ant-table-fixed-column`, `ant-table-scroll-horizontal`, `ant-table-scroll-vertical`, `ant-table-ping-left`, `ant-table-ping-right`, `ant-table-has-fix-left`, `ant-table-has-fix-right`, `ant-table-selection-column`, `ant-table-expand-icon-col`, `ant-table-row-expand-icon-cell`
- **特殊处理**:
  - 自动过滤选择列和展开列
  - 智能处理Ant Design组件（标签、按钮、徽章、头像等）
  - 提取状态指示器文本内容
  - 处理工具提示内容

## 核心功能

### 框架检测
转换器会自动检测表格使用的框架：
1. 检查元素及其祖先的CSS类
2. 匹配已知的框架特征
3. 应用相应的处理逻辑

### 智能表格提取
- **包装器处理**: 自动识别并穿透表格包装器
- **行提取**: 根据框架特性提取表格行
- **单元格处理**: 智能提取单元格内容，忽略装饰性元素

### 内容清理
- **管道字符转义**: 自动转义Markdown表格中的管道字符
- **空白标准化**: 规范化空白字符
- **换行处理**: 移除单元格内的换行符

### 标题行识别
- **显式标题**: 识别`<thead>`部分
- **隐式标题**: 检测包含`<th>`元素的行
- **框架特定**: 根据框架调整标题行数量

## 使用示例

### Bootstrap表格
```html
<div class="table-responsive">
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Name</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>John</td>
                <td><span class="badge badge-success">Active</span></td>
            </tr>
        </tbody>
    </table>
</div>
```

转换结果：
```markdown
| Name | Status |
|---|---|
| John | Active |
```

### DataTables表格
```html
<div class="dataTables_wrapper">
    <table class="display dataTable">
        <thead>
            <tr>
                <th class="sorting">Name</th>
                <th class="sorting">Position</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Tiger Nixon</td>
                <td>System Architect</td>
            </tr>
        </tbody>
    </table>
</div>
```

转换结果：
```markdown
| Name | Position |
|---|---|
| Tiger Nixon | System Architect |
```

### Ant Design表格
```html
<div class="ant-table-wrapper">
    <div class="ant-spin-nested-loading">
        <div class="ant-spin-container">
            <div class="ant-table">
                <div class="ant-table-container">
                    <div class="ant-table-content">
                        <table>
                            <thead class="ant-table-thead">
                                <tr>
                                    <th class="ant-table-cell">Name</th>
                                    <th class="ant-table-cell">Age</th>
                                    <th class="ant-table-cell">Status</th>
                                    <th class="ant-table-cell">Tags</th>
                                    <th class="ant-table-cell">Action</th>
                                </tr>
                            </thead>
                            <tbody class="ant-table-tbody">
                                <tr class="ant-table-row">
                                    <td class="ant-table-cell">John Brown</td>
                                    <td class="ant-table-cell">32</td>
                                    <td class="ant-table-cell">
                                        <span class="ant-badge ant-badge-status ant-badge-not-a-wrapper">
                                            <span class="ant-badge-status-dot ant-badge-status-success"></span>
                                            <span class="ant-badge-status-text">Active</span>
                                        </span>
                                    </td>
                                    <td class="ant-table-cell">
                                        <span class="ant-tag ant-tag-blue">Developer</span>
                                        <span class="ant-tag ant-tag-green">Nice</span>
                                    </td>
                                    <td class="ant-table-cell">
                                        <button class="ant-btn ant-btn-primary ant-btn-sm">Edit</button>
                                        <button class="ant-btn ant-btn-danger ant-btn-sm">Delete</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
```

转换结果：
```markdown
| Name | Age | Status | Tags | Action |
|---|---|---|---|---|
| John Brown | 32 | Active | Developer Nice | Edit Delete |
```

## 技术实现

### 架构设计
- **枚举框架**: `TableFramework`枚举定义支持的框架
- **检测逻辑**: `detectTableFramework()`方法识别框架
- **处理策略**: 每个框架有专门的处理逻辑

### 关键方法
- `detectTableFramework()`: 框架检测
- `findActualTableElement()`: 查找实际表格元素
- `extractEnhancedTableData()`: 增强数据提取
- `extractEnhancedCellContent()`: 智能单元格处理
- `determineHeaderRowCount()`: 标题行识别

### 扩展性
框架易于扩展，添加新框架支持只需：
1. 在`TableFramework`枚举中添加新框架
2. 定义框架特征CSS类
3. 实现框架特定的处理逻辑

## 测试覆盖

增强功能包含全面的测试覆盖：
- Bootstrap表格测试
- Tailwind CSS表格测试
- DataTables表格测试
- Material Design表格测试
- Foundation表格测试
- Semantic UI表格测试
- Ant Design表格测试（包括选择列和展开列）
- 复杂嵌套表格测试
- 管道字符转义测试
- 性能基准测试

## 性能优化

- **缓存机制**: 框架检测结果可缓存
- **选择器优化**: 使用高效的CSS选择器
- **内存管理**: 避免不必要的DOM克隆
- **批量处理**: 批量处理表格数据

## 兼容性

- **向后兼容**: 完全兼容现有表格处理
- **渐进增强**: 未识别框架时使用标准处理
- **错误处理**: 优雅处理异常情况

## 配置选项

当前版本自动检测框架，未来可考虑添加：
- 手动指定框架类型
- 自定义框架规则
- 处理策略配置

## 总结

增强的表格处理功能显著提升了HTML到Markdown转换器的实用性，特别是在处理现代Web应用中常见的框架化表格时。通过智能框架检测和专门的处理逻辑，确保了高质量的转换结果。
