package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.DocumentProcessingException;
import com.talkweb.ai.indexer.core.ProcessingContext;
import com.talkweb.ai.indexer.core.ProcessingResult;
import com.talkweb.ai.indexer.util.HtmlConversionMode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

class HtmlToMarkdownConverterTest {

    private HtmlToMarkdownConverter converter;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        converter = new HtmlToMarkdownConverter(null); // Metadata is not used in process
    }

    @Test
    void testSupportsExtensions() {
        assertTrue(converter.supports("html"));
        assertTrue(converter.supports("htm"));
        assertTrue(converter.supports(".html"));
        assertFalse(converter.supports("txt"));
        assertFalse(converter.supports(null));
    }

    @Test
    void testSimpleConversion() throws IOException, DocumentProcessingException {
        Path htmlFile = tempDir.resolve("test.html");
        Files.writeString(htmlFile, "<h1>Hello</h1><p>World</p>");

        ProcessingContext context = ProcessingContext.builder()
                .setOutputPath(tempDir)
                .setHtmlConversionMode(HtmlConversionMode.LOOSE)
                .build();
        ProcessingResult result = converter.process(htmlFile.toFile(), context);

        assertNotNull(result);
        assertNull(result.getError());
        assertNotNull(result.getOutputFile());

        String markdownContent = Files.readString(result.getOutputFile().toPath());
        assertEquals("# Hello\n\nWorld", markdownContent.trim());
    }

    @Test
    void testStrictConversionFailure() throws IOException {
        Path htmlFile = tempDir.resolve("invalid.html");
        Files.writeString(htmlFile, "<p>Unclosed tag");

        ProcessingContext context = ProcessingContext.builder()
                .setOutputPath(tempDir)
                .setHtmlConversionMode(HtmlConversionMode.STRICT)
                .build();

        assertThrows(DocumentProcessingException.class, () -> {
            converter.process(htmlFile.toFile(), context);
        });
    }

    @Test
    void testRelaxedConversionNoError() throws IOException, DocumentProcessingException {
        Path htmlFile = tempDir.resolve("invalid_relaxed.html");
        Files.writeString(htmlFile, "<p>Unclosed tag");

        ProcessingContext context = ProcessingContext.builder()
                .setOutputPath(tempDir)
                .setHtmlConversionMode(HtmlConversionMode.LOOSE)
                .build();
        ProcessingResult result = converter.process(htmlFile.toFile(), context);

        assertNotNull(result);
        // In relaxed mode, it might produce output or an error, depending on the underlying library
        // Here we just assert that it doesn't throw the strict exception
        assertTrue(result.getOutputFile() != null || result.getError() != null);
    }
}
