package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.*;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import org.apache.commons.io.FilenameUtils;

/**
 * 文档处理责任链实现
 */
public class DocumentProcessingChain implements DocumentProcessor {
    private final List<DocumentProcessor> processors = new ArrayList<>();
    private PluginMetadata metadata;
    private PluginState state = PluginState.REGISTERED;

    public DocumentProcessingChain(PluginMetadata metadata) {
        this.metadata = metadata;
    }
    public DocumentProcessingChain(PluginMetadata metadata, List<DocumentProcessor> processors) {
        this.metadata = metadata;
        this.processors.addAll(processors);
    }
    public DocumentProcessingChain(PluginMetadata metadata, DocumentProcessor... processors) {
        this.metadata = metadata;
        this.processors.addAll(Arrays.asList(processors));
    }
    public DocumentProcessingChain(PluginMetadata metadata, DocumentProcessor processor) {
        this.metadata = metadata;
        this.processors.add(processor);
    }
    public DocumentProcessingChain(List<DocumentProcessor> processors) {
        this.processors.addAll(processors);
    }
    @Override
    public PluginMetadata getMetadata() {
        return metadata;
    }

    @Override
    public PluginState getState() {
        return state;
    }

    @Override
    public void init(PluginContext context) {
        state = PluginState.READY;
    }

    @Override
    public void start() {
        state = PluginState.RUNNING;
    }

    @Override
    public void stop() {
        state = PluginState.STOPPED;
    }

    @Override
    public void destroy() {
        state = PluginState.DESTROYED;
    }

    @Override
    public String[] getSupportedExtensions() {
        return processors.stream()
            .flatMap(p -> Arrays.stream(p.getSupportedExtensions()))
            .distinct()
            .toArray(String[]::new);
    }

    @Override
    public boolean supports(String extension) {
        return processors.stream().anyMatch(p -> p.supports(extension));
    }

    @Override
    public ProcessingResult process(File inputFile, ProcessingContext context) throws DocumentProcessingException {
        String ext = getFileExtension(inputFile.getName());
        for (DocumentProcessor processor : processors) {
            if (processor.supports(ext)) {
                return processor.process(inputFile, context);
            }
        }
        throw new UnsupportedOperationException("No processor found for file: " + inputFile.getName());
    }

    public void addProcessor(DocumentProcessor processor) {
        processors.add(processor);
    }

    private String getFileExtension(String filename) {
        int dotIndex = filename.lastIndexOf('.');
        return (dotIndex == -1) ? "" : filename.substring(dotIndex + 1);
    }

    @Override
    public Map<String, Object> getConfiguration() {
        return Collections.emptyMap();
    }

    @Override
    public void configure(Map<String, Object> config) {
        // 配置处理链中的各个处理器
        processors.forEach(p -> p.configure(config));
    }
}
