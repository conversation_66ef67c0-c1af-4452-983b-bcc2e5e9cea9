
package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.*;
import com.talkweb.ai.indexer.util.HtmlConversionMode;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Collections;
import java.util.Map;

public class HtmlToMarkdownConverter implements DocumentProcessor, Plugin {

    private final PluginMetadata metadata;
    private boolean initialized = true;
    private Map<String, Object> configuration = Collections.emptyMap();

    public HtmlToMarkdownConverter(PluginMetadata metadata) {
        this.metadata = metadata;
    }

    @Override
    public void destroy() {
        this.initialized = false;
    }
    
    @Override
    public void stop() throws PluginException {
        this.initialized = false;
    }

    @Override
    public PluginMetadata getMetadata() {
        return metadata;
    }

    @Override
    public PluginState getState() {
        return initialized ? PluginState.RUNNING : PluginState.STOPPED;
    }

    @Override
    public void init(PluginContext context) throws PluginException {
        this.initialized = true;
    }

    @Override
    public void start() throws PluginException {
        this.initialized = true;
    }

    @Override
    public String[] getSupportedExtensions() {
        return new String[]{"html", "htm"};
    }

    @Override
    public boolean supports(String extension) {
        if (extension == null) {
            return false;
        }
        String cleanExtension = extension.startsWith(".") ? extension.substring(1) : extension;
        for (String supportedExt : getSupportedExtensions()) {
            if (supportedExt.equalsIgnoreCase(cleanExtension)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Map<String, Object> getConfiguration() {
        return configuration;
    }

    @Override
    public void configure(Map<String, Object> config) {
        if (config != null) {
            this.configuration = config;
        }
    }

    @Override
    public ProcessingResult process(File inputFile, ProcessingContext context) throws DocumentProcessingException {
        HtmlConversionMode mode = context.getHtmlConversionMode();
        Path outputPath = Path.of(inputFile.getParent(), inputFile.getName() + ".md");
        try {
            String htmlContent = java.nio.file.Files.readString(inputFile.toPath());
            String markdown = com.talkweb.ai.indexer.util.HtmlToMarkdownConverter.convert(htmlContent, mode);

            Path outputFilePath = Path.of(outputPath.toString());
            try {
                java.nio.file.Files.writeString(outputFilePath, markdown.trim());
            } catch (IOException e) {
                return ProcessingResult.failure(e);
            }
            return ProcessingResult.success(outputFilePath.toFile());
        } catch (Exception e) {
            if (mode == HtmlConversionMode.STRICT) {
                throw new DocumentProcessingException("HTML to Markdown conversion failed in STRICT mode for file: " + inputFile.getPath(), e);
            }
            return ProcessingResult.failure(e);
        }
    }


}
