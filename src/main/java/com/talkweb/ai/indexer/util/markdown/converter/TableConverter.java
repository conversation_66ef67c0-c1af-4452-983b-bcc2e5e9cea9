package com.talkweb.ai.indexer.util.markdown.converter;

import com.talkweb.ai.indexer.util.markdown.ConversionContext;
import com.talkweb.ai.indexer.util.markdown.MarkdownBuilder;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static com.talkweb.ai.indexer.util.markdown.MarkdownConstants.*;

/**
 * Converter for HTML table elements
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class TableConverter extends AbstractElementConverter {
    
    private static final Set<String> SUPPORTED_TAGS = Set.of(
        TAG_TABLE, TAG_TR, TAG_TD, TAG_TH, TAG_THEAD, TAG_TBODY, TAG_TFOOT
    );
    
    @Override
    public Set<String> getSupportedTags() {
        return SUPPORTED_TAGS;
    }
    
    @Override
    public void convert(Element element, MarkdownBuilder builder, ConversionContext context) 
            throws ConversionException {
        validate(element, context);
        
        String tagName = element.tagName().toLowerCase();
        
        switch (tagName) {
            case TAG_TABLE:
                convertTable(element, builder, context);
                break;
            case TAG_THEAD:
            case TAG_TBODY:
            case TAG_TFOOT:
                // These are handled by the table converter
                processChildren(element, builder, context);
                break;
            case TAG_TR:
            case TAG_TD:
            case TAG_TH:
                // These are handled by the table converter
                break;
            default:
                throw new ConversionException("Unsupported table tag: " + tagName);
        }
    }
    
    @Override
    public boolean shouldProcessChildren(Element element, ConversionContext context) {
        String tagName = element.tagName().toLowerCase();
        // Only table and section elements should process children normally
        return TAG_TABLE.equals(tagName) || TAG_THEAD.equals(tagName) || 
               TAG_TBODY.equals(tagName) || TAG_TFOOT.equals(tagName);
    }
    
    @Override
    public int getPriority() {
        return 60; // Medium priority for tables
    }
    
    @Override
    public void beforeConvert(Element element, MarkdownBuilder builder, ConversionContext context) {
        if (TAG_TABLE.equals(element.tagName().toLowerCase())) {
            context.setInTable(true);
        }
    }
    
    @Override
    public void afterConvert(Element element, MarkdownBuilder builder, ConversionContext context) {
        if (TAG_TABLE.equals(element.tagName().toLowerCase())) {
            context.setInTable(false);
        }
    }
    
    /**
     * Converts a complete table to Markdown
     *
     * @param table the table element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    private void convertTable(Element table, MarkdownBuilder builder, ConversionContext context) {
        if (!builder.isEmpty()) {
            builder.newline();
        }
        
        Elements rows = table.select("tr");
        if (rows.isEmpty()) {
            return;
        }
        
        List<List<String>> tableData = extractTableData(rows, context);
        if (tableData.isEmpty()) {
            return;
        }
        
        int maxCols = tableData.stream().mapToInt(List::size).max().orElse(0);
        if (maxCols == 0) {
            return;
        }
        
        // Normalize table data (ensure all rows have same number of columns)
        normalizeTableData(tableData, maxCols);
        
        // Write table header
        writeTableHeader(tableData.get(0), builder);
        
        // Write separator row
        writeTableSeparator(maxCols, builder);
        
        // Write data rows
        for (int i = 1; i < tableData.size(); i++) {
            writeTableRow(tableData.get(i), builder);
        }

        builder.newline();
    }
    
    /**
     * Extracts table data from table rows
     * 
     * @param rows the table rows
     * @param context the conversion context
     * @return list of table data rows
     */
    private List<List<String>> extractTableData(Elements rows, ConversionContext context) {
        List<List<String>> tableData = new ArrayList<>();
        
        for (Element row : rows) {
            List<String> rowData = new ArrayList<>();
            Elements cells = row.select("td, th");
            
            for (Element cell : cells) {
                String cellContent = extractCellContent(cell, context);
                rowData.add(cellContent);
            }
            
            if (!rowData.isEmpty()) {
                tableData.add(rowData);
            }
        }
        
        return tableData;
    }
    
    /**
     * Extracts content from a table cell
     *
     * @param cell the table cell
     * @param context the conversion context
     * @return the cell content
     */
    private String extractCellContent(Element cell, ConversionContext context) {
        // Extract text content directly
        String content = extractTextContent(cell);

        // Escape pipe characters in cell content
        content = content.replace(TABLE_SEPARATOR, "\\" + TABLE_SEPARATOR);

        // Remove newlines from cell content
        content = content.replace(NEWLINE, SPACE);

        return content;
    }
    
    /**
     * Normalizes table data to ensure all rows have the same number of columns
     * 
     * @param tableData the table data
     * @param maxCols the maximum number of columns
     */
    private void normalizeTableData(List<List<String>> tableData, int maxCols) {
        for (List<String> row : tableData) {
            while (row.size() < maxCols) {
                row.add(EMPTY_STRING);
            }
        }
    }
    
    /**
     * Writes a table header row
     * 
     * @param headerRow the header row data
     * @param builder the markdown builder
     */
    private void writeTableHeader(List<String> headerRow, MarkdownBuilder builder) {
        builder.append(TABLE_SEPARATOR);
        for (String cell : headerRow) {
            builder.append(SPACE).append(cell).append(SPACE).append(TABLE_SEPARATOR);
        }
        builder.newline();
    }
    
    /**
     * Writes the table separator row
     * 
     * @param numCols the number of columns
     * @param builder the markdown builder
     */
    private void writeTableSeparator(int numCols, MarkdownBuilder builder) {
        builder.append(TABLE_SEPARATOR);
        for (int i = 0; i < numCols; i++) {
            builder.append(TABLE_HEADER_SEPARATOR);
        }
        builder.newline();
    }
    
    /**
     * Writes a table data row
     * 
     * @param row the row data
     * @param builder the markdown builder
     */
    private void writeTableRow(List<String> row, MarkdownBuilder builder) {
        builder.append(TABLE_SEPARATOR);
        for (String cell : row) {
            builder.append(SPACE).append(cell).append(SPACE).append(TABLE_SEPARATOR);
        }
        builder.newline();
    }
}
