package com.talkweb.ai.indexer.util.markdown.converter;

import com.talkweb.ai.indexer.util.markdown.ConversionContext;
import com.talkweb.ai.indexer.util.markdown.MarkdownBuilder;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.*;

import static com.talkweb.ai.indexer.util.markdown.MarkdownConstants.*;

/**
 * Enhanced converter for HTML table elements with framework compatibility
 *
 * Supports:
 * - Bootstrap tables (table, table-striped, table-bordered, etc.)
 * - Tailwind CSS tables (table-auto, table-fixed, etc.)
 * - jQuery DataTables
 * - Material Design tables
 * - Foundation tables
 * - Semantic UI tables
 * - Custom styled tables
 *
 * <AUTHOR> Assistant
 * @version 2.0
 */
public class TableConverter extends AbstractElementConverter {

    private static final Set<String> SUPPORTED_TAGS = Set.of(
        TAG_TABLE, TAG_TR, TAG_TD, TAG_TH, TAG_THEAD, TAG_TBODY, TAG_TFOOT
    );

    // Framework-specific CSS classes for table identification
    private static final Set<String> BOOTSTRAP_TABLE_CLASSES = Set.of(
        "table", "table-striped", "table-bordered", "table-hover", "table-condensed",
        "table-responsive", "table-dark", "table-light", "table-sm", "table-lg"
    );

    private static final Set<String> TAILWIND_TABLE_CLASSES = Set.of(
        "table-auto", "table-fixed", "border-collapse", "border-separate",
        "table-caption", "table-cell", "table-column", "table-column-group",
        "table-footer-group", "table-header-group", "table-row", "table-row-group"
    );

    private static final Set<String> DATATABLE_CLASSES = Set.of(
        "dataTable", "dataTables_wrapper", "dataTables_length", "dataTables_filter",
        "dataTables_info", "dataTables_paginate", "display", "cell-border", "stripe"
    );

    private static final Set<String> MATERIAL_TABLE_CLASSES = Set.of(
        "mdl-data-table", "mdc-data-table", "mat-table", "material-table"
    );

    private static final Set<String> FOUNDATION_TABLE_CLASSES = Set.of(
        "table", "stack", "scroll", "hover", "unstriped"
    );

    private static final Set<String> SEMANTIC_TABLE_CLASSES = Set.of(
        "ui", "table", "celled", "striped", "selectable", "inverted", "definition"
    );

    private static final Set<String> ANT_DESIGN_TABLE_CLASSES = Set.of(
        "ant-table", "ant-table-wrapper", "ant-table-container", "ant-table-content",
        "ant-table-thead", "ant-table-tbody", "ant-table-tfoot", "ant-table-cell",
        "ant-table-row", "ant-table-header", "ant-table-body", "ant-table-footer",
        "ant-table-bordered", "ant-table-striped", "ant-table-hover", "ant-table-small",
        "ant-table-middle", "ant-table-large", "ant-table-fixed-header", "ant-table-fixed-column",
        "ant-table-scroll-horizontal", "ant-table-scroll-vertical", "ant-table-ping-left",
        "ant-table-ping-right", "ant-table-has-fix-left", "ant-table-has-fix-right",
        "ant-table-selection-column", "ant-table-expand-icon-col", "ant-table-row-expand-icon-cell"
    );

    // Common table wrapper classes that should be ignored
    private static final Set<String> WRAPPER_CLASSES = Set.of(
        "table-responsive", "table-wrapper", "dataTables_wrapper", "table-container",
        "overflow-x-auto", "overflow-auto", "scroll-container", "ant-table-wrapper",
        "ant-table-container", "ant-spin-nested-loading", "ant-spin-container"
    );

    /**
     * Enum representing different table frameworks
     */
    private enum TableFramework {
        BOOTSTRAP,
        TAILWIND,
        DATATABLE,
        MATERIAL,
        FOUNDATION,
        SEMANTIC,
        ANT_DESIGN,
        STANDARD
    }
    
    @Override
    public Set<String> getSupportedTags() {
        return SUPPORTED_TAGS;
    }
    
    @Override
    public void convert(Element element, MarkdownBuilder builder, ConversionContext context) 
            throws ConversionException {
        validate(element, context);
        
        String tagName = element.tagName().toLowerCase();
        
        switch (tagName) {
            case TAG_TABLE:
                convertTable(element, builder, context);
                break;
            case TAG_THEAD:
            case TAG_TBODY:
            case TAG_TFOOT:
                // These are handled by the table converter
                processChildren(element, builder, context);
                break;
            case TAG_TR:
            case TAG_TD:
            case TAG_TH:
                // These are handled by the table converter
                break;
            default:
                throw new ConversionException("Unsupported table tag: " + tagName);
        }
    }
    
    @Override
    public boolean shouldProcessChildren(Element element, ConversionContext context) {
        String tagName = element.tagName().toLowerCase();
        // Only table and section elements should process children normally
        return TAG_TABLE.equals(tagName) || TAG_THEAD.equals(tagName) || 
               TAG_TBODY.equals(tagName) || TAG_TFOOT.equals(tagName);
    }
    
    @Override
    public int getPriority() {
        return 60; // Medium priority for tables
    }
    
    @Override
    public void beforeConvert(Element element, MarkdownBuilder builder, ConversionContext context) {
        if (TAG_TABLE.equals(element.tagName().toLowerCase())) {
            context.setInTable(true);
        }
    }
    
    @Override
    public void afterConvert(Element element, MarkdownBuilder builder, ConversionContext context) {
        if (TAG_TABLE.equals(element.tagName().toLowerCase())) {
            context.setInTable(false);
        }
    }
    
    /**
     * Converts a complete table to Markdown with enhanced framework compatibility
     *
     * @param table the table element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    private void convertTable(Element table, MarkdownBuilder builder, ConversionContext context) {
        if (!builder.isEmpty()) {
            builder.newline();
        }

        // Detect table framework and apply appropriate processing
        TableFramework framework = detectTableFramework(table);

        // Find the actual table element if wrapped
        Element actualTable = findActualTableElement(table);
        if (actualTable == null) {
            return;
        }

        // Extract rows with framework-aware logic
        Elements rows = extractTableRows(actualTable, framework);
        if (rows.isEmpty()) {
            return;
        }

        // Extract table data with enhanced cell processing
        List<List<String>> tableData = extractEnhancedTableData(rows, context, framework);
        if (tableData.isEmpty()) {
            return;
        }

        int maxCols = tableData.stream().mapToInt(List::size).max().orElse(0);
        if (maxCols == 0) {
            return;
        }

        // Normalize table data (ensure all rows have same number of columns)
        normalizeTableData(tableData, maxCols);

        // Determine header rows based on framework
        int headerRowCount = determineHeaderRowCount(actualTable, framework);

        // Write table with proper header handling
        writeEnhancedTable(tableData, headerRowCount, maxCols, builder);

        builder.newline();
    }
    

    
    /**
     * Normalizes table data to ensure all rows have the same number of columns
     * 
     * @param tableData the table data
     * @param maxCols the maximum number of columns
     */
    private void normalizeTableData(List<List<String>> tableData, int maxCols) {
        for (List<String> row : tableData) {
            while (row.size() < maxCols) {
                row.add(EMPTY_STRING);
            }
        }
    }
    
    /**
     * Writes a table header row
     * 
     * @param headerRow the header row data
     * @param builder the markdown builder
     */
    private void writeTableHeader(List<String> headerRow, MarkdownBuilder builder) {
        builder.append(TABLE_SEPARATOR);
        for (String cell : headerRow) {
            builder.append(SPACE).append(cell).append(SPACE).append(TABLE_SEPARATOR);
        }
        builder.newline();
    }
    
    /**
     * Writes the table separator row
     * 
     * @param numCols the number of columns
     * @param builder the markdown builder
     */
    private void writeTableSeparator(int numCols, MarkdownBuilder builder) {
        builder.append(TABLE_SEPARATOR);
        for (int i = 0; i < numCols; i++) {
            builder.append(TABLE_HEADER_SEPARATOR);
        }
        builder.newline();
    }
    
    /**
     * Writes a table data row
     *
     * @param row the row data
     * @param builder the markdown builder
     */
    private void writeTableRow(List<String> row, MarkdownBuilder builder) {
        builder.append(TABLE_SEPARATOR);
        for (String cell : row) {
            builder.append(SPACE).append(cell).append(SPACE).append(TABLE_SEPARATOR);
        }
        builder.newline();
    }

    // ========== Enhanced Framework-Aware Methods ==========

    /**
     * Detects the table framework based on CSS classes and structure
     *
     * @param element the table or wrapper element
     * @return the detected framework
     */
    private TableFramework detectTableFramework(Element element) {
        // Check element and its ancestors for framework classes
        Element current = element;
        while (current != null) {
            String className = current.className().toLowerCase();
            Set<String> classes = Set.of(className.split("\\s+"));

            // Check for Bootstrap classes
            if (classes.stream().anyMatch(BOOTSTRAP_TABLE_CLASSES::contains)) {
                return TableFramework.BOOTSTRAP;
            }

            // Check for Tailwind classes
            if (classes.stream().anyMatch(TAILWIND_TABLE_CLASSES::contains)) {
                return TableFramework.TAILWIND;
            }

            // Check for DataTable classes
            if (classes.stream().anyMatch(DATATABLE_CLASSES::contains)) {
                return TableFramework.DATATABLE;
            }

            // Check for Material Design classes
            if (classes.stream().anyMatch(MATERIAL_TABLE_CLASSES::contains)) {
                return TableFramework.MATERIAL;
            }

            // Check for Foundation classes
            if (classes.stream().anyMatch(FOUNDATION_TABLE_CLASSES::contains)) {
                return TableFramework.FOUNDATION;
            }

            // Check for Semantic UI classes
            if (classes.stream().anyMatch(SEMANTIC_TABLE_CLASSES::contains)) {
                return TableFramework.SEMANTIC;
            }

            // Check for Ant Design classes
            if (classes.stream().anyMatch(ANT_DESIGN_TABLE_CLASSES::contains)) {
                return TableFramework.ANT_DESIGN;
            }

            current = current.parent();
        }

        return TableFramework.STANDARD;
    }

    /**
     * Finds the actual table element, handling framework wrappers
     *
     * @param element the starting element
     * @return the actual table element or null if not found
     */
    private Element findActualTableElement(Element element) {
        // If it's already a table, return it
        if (TAG_TABLE.equals(element.tagName().toLowerCase())) {
            return element;
        }

        // Look for table in children (handle wrappers)
        Elements tables = element.select("table");
        if (!tables.isEmpty()) {
            return tables.first();
        }

        return null;
    }

    /**
     * Extracts table rows with framework-aware logic
     *
     * @param table the table element
     * @param framework the detected framework
     * @return the table rows
     */
    private Elements extractTableRows(Element table, TableFramework framework) {
        switch (framework) {
            case DATATABLE:
                // DataTables might have additional wrapper rows
                return table.select("tbody tr, thead tr, tfoot tr");
            case MATERIAL:
                // Material tables might use different selectors
                Elements materialRows = table.select("tr");
                if (materialRows.isEmpty()) {
                    materialRows = table.select(".mdc-data-table__row, .mat-row");
                }
                return materialRows;
            case ANT_DESIGN:
                // Ant Design tables might have specific row classes
                Elements antRows = table.select("tr");
                if (antRows.isEmpty()) {
                    antRows = table.select(".ant-table-row, .ant-table-header-row");
                }
                return antRows;
            default:
                return table.select("tr");
        }
    }

    /**
     * Extracts table data with enhanced cell processing for different frameworks
     *
     * @param rows the table rows
     * @param context the conversion context
     * @param framework the detected framework
     * @return list of table data rows
     */
    private List<List<String>> extractEnhancedTableData(Elements rows, ConversionContext context,
                                                        TableFramework framework) {
        List<List<String>> tableData = new ArrayList<>();

        for (Element row : rows) {
            List<String> rowData = extractRowData(row, context, framework);
            if (!rowData.isEmpty()) {
                tableData.add(rowData);
            }
        }

        return tableData;
    }

    /**
     * Extracts data from a single row with framework-specific logic
     *
     * @param row the table row
     * @param context the conversion context
     * @param framework the detected framework
     * @return the row data
     */
    private List<String> extractRowData(Element row, ConversionContext context, TableFramework framework) {
        List<String> rowData = new ArrayList<>();

        // Get cells based on framework
        Elements cells = getCellsForFramework(row, framework);

        for (Element cell : cells) {
            String cellContent = extractEnhancedCellContent(cell, context, framework);
            rowData.add(cellContent);
        }

        return rowData;
    }

    /**
     * Gets cells from a row based on the framework
     *
     * @param row the table row
     * @param framework the detected framework
     * @return the cells
     */
    private Elements getCellsForFramework(Element row, TableFramework framework) {
        switch (framework) {
            case MATERIAL:
                // Material Design might use different cell selectors
                Elements materialCells = row.select("td, th");
                if (materialCells.isEmpty()) {
                    materialCells = row.select(".mdc-data-table__cell, .mat-cell");
                }
                return materialCells;
            case DATATABLE:
                // DataTables might have additional cell classes
                return row.select("td, th");
            case ANT_DESIGN:
                // Ant Design might use specific cell classes
                Elements antCells = row.select("td, th");
                if (antCells.isEmpty()) {
                    antCells = row.select(".ant-table-cell");
                }
                return antCells;
            default:
                return row.select("td, th");
        }
    }

    /**
     * Extracts content from a table cell with framework-specific enhancements
     *
     * @param cell the table cell
     * @param context the conversion context
     * @param framework the detected framework
     * @return the cell content
     */
    private String extractEnhancedCellContent(Element cell, ConversionContext context, TableFramework framework) {
        String content;

        // Handle framework-specific cell content extraction
        switch (framework) {
            case DATATABLE:
                // DataTables might have sorting controls or other elements to ignore
                content = extractDataTableCellContent(cell);
                break;
            case MATERIAL:
                // Material Design cells might have specific structure
                content = extractMaterialCellContent(cell);
                break;
            case BOOTSTRAP:
                // Bootstrap tables might have additional styling elements
                content = extractBootstrapCellContent(cell);
                break;
            case ANT_DESIGN:
                // Ant Design tables might have specific components
                content = extractAntDesignCellContent(cell);
                break;
            default:
                content = extractTextContent(cell);
                break;
        }

        // Common post-processing
        content = postProcessCellContent(content);

        return content;
    }

    /**
     * Extracts content from DataTable cells, ignoring sorting controls
     *
     * @param cell the table cell
     * @return the cell content
     */
    private String extractDataTableCellContent(Element cell) {
        // Clone the cell to avoid modifying the original
        Element cellCopy = cell.clone();

        // Remove DataTable-specific elements
        cellCopy.select(".sorting, .sorting_asc, .sorting_desc").remove();
        cellCopy.select(".dataTables_sizing").remove();

        return extractTextContent(cellCopy);
    }

    /**
     * Extracts content from Material Design cells
     *
     * @param cell the table cell
     * @return the cell content
     */
    private String extractMaterialCellContent(Element cell) {
        // Material cells might have specific content structure
        Element contentElement = cell.selectFirst(".mdc-data-table__cell-content, .mat-cell-content");
        if (contentElement != null) {
            return extractTextContent(contentElement);
        }

        return extractTextContent(cell);
    }

    /**
     * Extracts content from Bootstrap table cells
     *
     * @param cell the table cell
     * @return the cell content
     */
    private String extractBootstrapCellContent(Element cell) {
        // Bootstrap might have badges, buttons, or other components in cells
        Element cellCopy = cell.clone();

        // Preserve important content but clean up styling
        Elements badges = cellCopy.select(".badge");
        for (Element badge : badges) {
            badge.replaceWith(new org.jsoup.nodes.TextNode(badge.text()));
        }

        return extractTextContent(cellCopy);
    }

    /**
     * Extracts content from Ant Design table cells
     *
     * @param cell the table cell
     * @return the cell content
     */
    private String extractAntDesignCellContent(Element cell) {
        // Clone the cell to avoid modifying the original
        Element cellCopy = cell.clone();

        // Remove Ant Design specific elements that shouldn't be in content
        cellCopy.select(".ant-table-selection-column").remove();
        cellCopy.select(".ant-table-expand-icon-col").remove();
        cellCopy.select(".ant-table-row-expand-icon").remove();
        cellCopy.select(".ant-table-row-indent").remove();

        // Handle Ant Design components with proper spacing
        Elements tags = cellCopy.select(".ant-tag");
        for (Element tag : tags) {
            String tagText = tag.text();
            if (!tagText.isEmpty()) {
                tag.replaceWith(new org.jsoup.nodes.TextNode(tagText + " "));
            } else {
                tag.remove();
            }
        }

        Elements buttons = cellCopy.select(".ant-btn");
        for (Element button : buttons) {
            String buttonText = button.text();
            if (!buttonText.isEmpty()) {
                button.replaceWith(new org.jsoup.nodes.TextNode(buttonText + " "));
            } else {
                button.remove();
            }
        }

        Elements badges = cellCopy.select(".ant-badge");
        for (Element badge : badges) {
            // Extract badge text, might be in .ant-badge-count or direct text
            Element badgeCount = badge.selectFirst(".ant-badge-count");
            String badgeText = badgeCount != null ? badgeCount.text() : badge.text();
            badge.replaceWith(new org.jsoup.nodes.TextNode(badgeText));
        }

        Elements avatars = cellCopy.select(".ant-avatar");
        for (Element avatar : avatars) {
            // For avatars, we might want to extract alt text or just remove them
            String altText = avatar.attr("alt");
            if (!altText.isEmpty()) {
                avatar.replaceWith(new org.jsoup.nodes.TextNode(altText));
            } else {
                avatar.remove();
            }
        }

        Elements tooltips = cellCopy.select(".ant-tooltip");
        for (Element tooltip : tooltips) {
            // Extract tooltip content
            Element tooltipInner = tooltip.selectFirst(".ant-tooltip-inner");
            if (tooltipInner != null) {
                tooltip.replaceWith(new org.jsoup.nodes.TextNode(tooltipInner.text()));
            } else {
                tooltip.replaceWith(new org.jsoup.nodes.TextNode(tooltip.text()));
            }
        }

        // Handle status indicators
        Elements statusDots = cellCopy.select(".ant-badge-status-dot");
        statusDots.remove(); // Remove visual status dots

        Elements statusText = cellCopy.select(".ant-badge-status-text");
        for (Element status : statusText) {
            status.replaceWith(new org.jsoup.nodes.TextNode(status.text()));
        }

        return extractTextContent(cellCopy);
    }

    /**
     * Post-processes cell content for markdown compatibility
     *
     * @param content the raw cell content
     * @return the processed content
     */
    private String postProcessCellContent(String content) {
        if (content == null) {
            return EMPTY_STRING;
        }

        // Escape pipe characters in cell content
        content = content.replace(TABLE_SEPARATOR, "\\" + TABLE_SEPARATOR);

        // Remove newlines from cell content
        content = content.replace(NEWLINE, SPACE);

        // Normalize whitespace
        content = content.replaceAll("\\s+", SPACE).trim();

        return content;
    }

    /**
     * Determines the number of header rows based on table structure and framework
     *
     * @param table the table element
     * @param framework the detected framework
     * @return the number of header rows
     */
    private int determineHeaderRowCount(Element table, TableFramework framework) {
        // Check for explicit thead section
        Elements theadRows = table.select("thead tr");
        if (!theadRows.isEmpty()) {
            return theadRows.size();
        }

        // Check for th elements in first rows
        Elements allRows = table.select("tr");
        if (allRows.isEmpty()) {
            return 0;
        }

        int headerRowCount = 0;
        for (Element row : allRows) {
            Elements thCells = row.select("th");
            if (!thCells.isEmpty()) {
                headerRowCount++;
            } else {
                break; // Stop at first row without th elements
            }
        }

        // Framework-specific adjustments
        switch (framework) {
            case DATATABLE:
                // DataTables typically have one header row
                return Math.max(1, headerRowCount);
            case MATERIAL:
                // Material tables usually have one header row
                return Math.max(1, headerRowCount);
            case ANT_DESIGN:
                // Ant Design tables typically have one header row
                return Math.max(1, headerRowCount);
            default:
                return Math.max(1, headerRowCount); // At least one header row
        }
    }

    /**
     * Writes an enhanced table with proper header handling
     *
     * @param tableData the table data
     * @param headerRowCount the number of header rows
     * @param maxCols the maximum number of columns
     * @param builder the markdown builder
     */
    private void writeEnhancedTable(List<List<String>> tableData, int headerRowCount,
                                   int maxCols, MarkdownBuilder builder) {
        if (tableData.isEmpty()) {
            return;
        }

        // Write header rows
        for (int i = 0; i < Math.min(headerRowCount, tableData.size()); i++) {
            writeTableHeader(tableData.get(i), builder);
            if (i == 0) {
                // Write separator after first header row
                writeTableSeparator(maxCols, builder);
            }
        }

        // If no header was written, write the first row as header
        if (headerRowCount == 0 && !tableData.isEmpty()) {
            writeTableHeader(tableData.get(0), builder);
            writeTableSeparator(maxCols, builder);
            headerRowCount = 1;
        }

        // Write data rows
        for (int i = headerRowCount; i < tableData.size(); i++) {
            writeTableRow(tableData.get(i), builder);
        }
    }
}
