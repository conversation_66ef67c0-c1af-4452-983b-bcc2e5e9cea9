
package com.talkweb.ai.indexer.util;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Safelist;

/**
 * Converts HTML to Markdown format
 */
public class HtmlToMarkdownConverter {

    /**
     * Converts HTML string to Markdown format using default LOOSE mode.
     * @param html HTML content to convert
     * @return Markdown formatted string
     */
    public static String convert(String html) {
        return convert(html, HtmlConversionMode.LOOSE);
    }

    /**
     * Converts HTML string to Markdown format
     * @param html HTML content to convert
     * @param mode Conversion mode (STRICT or LOOSE)
     * @return Markdown formatted string
     */
    public static String convert(String html, HtmlConversionMode mode) {
        try {
            if (html == null || html.isEmpty()) {
                return "";
            }

            if (mode == HtmlConversionMode.STRICT) {
                // In strict mode, we can add more checks, for now, we'll just clean it.
                // A more robust implementation would involve a proper parser and validation.
                Document.OutputSettings settings = new Document.OutputSettings().prettyPrint(false);
                String cleanedHtml = Jsoup.clean(html, "", Safelist.basic(), settings);
                if (!html.trim().equals(cleanedHtml.trim())) {
                    // This is a simplistic check. A real implementation would be more sophisticated.
                    throw new IllegalArgumentException("HTML contains unsupported tags in STRICT mode.");
                }
            }

            String markdown = html;

            // First decode HTML entities
            markdown = markdown.replace("&lt;", "<")
                              .replace("&gt;", ">")
                              .replace("&amp;", "&")
                              .replace("&quot;", "\"")
                              .replace("&apos;", "'");

            // Convert headers
            markdown = markdown.replaceAll("<h1>(.*?)</h1>", "# $1\n\n")
                              .replaceAll("<h2>(.*?)</h2>", "## $1\n\n")
                              .replaceAll("<h3>(.*?)</h3>", "### $1\n\n");

            // Convert paragraphs
            markdown = markdown.replaceAll("<p>(.*?)</p>", "$1\n\n");

            // Convert links
            markdown = markdown.replaceAll("<a href=\"(.*?)\">(.*?)</a>", "[$2]($1)");

            // Convert text formatting
            markdown = markdown.replaceAll("<strong>(.*?)</strong>", "**$1**")
                              .replaceAll("<b>(.*?)</b>", "**$1**")
                              .replaceAll("<em>(.*?)</em>", "*$1*")
                              .replaceAll("<i>(.*?)</i>", "*$1*");

            // Convert lists
            markdown = markdown.replaceAll("<li>(.*?)</li>", "- $1\n")
                              .replaceAll("<ul>|</ul>|<ol>|</ol>", "");

            // Convert code blocks
            markdown = markdown.replaceAll("<code>(.*?)</code>", "`$1`")
                              .replaceAll("<pre>(.*?)</pre>", "```\n$1\n```");

            // Convert blockquotes
            markdown = markdown.replaceAll("<blockquote>(.*?)</blockquote>", "> $1\n\n");

            // Convert horizontal rules
            markdown = markdown.replaceAll("<hr/?>", "---\n");

            // Convert images
            markdown = markdown.replaceAll("<img src=\"(.*?)\" alt=\"(.*?)\"?>", "![$2]($1)");

            // Clean up whitespace
            markdown = markdown.replaceAll("\n{3,}", "\n\n")
                              .replaceAll("\\s+$", "")
                              .trim();

            return markdown;
        } catch (Exception e) {
            if (mode == HtmlConversionMode.STRICT) {
                throw new RuntimeException("Failed to convert HTML in STRICT mode", e);
            }
            // Fallback to returning original HTML if conversion fails in LOOSE mode
            return html;
        }
    }
}
