package com.talkweb.ai.indexer.config;

import java.util.*;
import java.util.function.Function;

/**
 * 配置抽象基类，提供通用实现
 */
public abstract class AbstractConfiguration implements AppConfiguration {
    
    @Override
    public String getString(String key, String defaultValue) {
        return getValue(key, String.class).orElse(defaultValue);
    }
    
    @Override
    public int getInt(String key, int defaultValue) {
        return getValue(key, Integer.class).orElse(defaultValue);
    }
    
    @Override
    public long getLong(String key, long defaultValue) {
        return getValue(key, Long.class).orElse(defaultValue);
    }
    
    @Override
    public double getDouble(String key, double defaultValue) {
        return getValue(key, Double.class).orElse(defaultValue);
    }
    
    @Override
    public boolean getBoolean(String key, boolean defaultValue) {
        return getValue(key, Boolean.class).orElse(defaultValue);
    }

    @Override
    public List<String> getStringList(String key) {
        Object value = getRawValue(key);
        if (value == null) {
            return Collections.emptyList();
        }
        
        if (value instanceof List) {
            List<String> result = new ArrayList<>();
            for (Object item : (List<?>) value) {
                if (item != null) {
                    result.add(item.toString());
                }
            }
            return result;
        }
        return Collections.singletonList(value.toString());
    }

    @Override
    public Object getRawValue(String key) {
        throw new UnsupportedOperationException("getRawValue not implemented");
    }

    @Override
    public Iterable<String> getKeys() {
        throw new UnsupportedOperationException("getKeys not implemented");
    }

    @Override
    public Optional<AppConfiguration> getConfig(String key) {
        Object value = getRawValue(key);
        if (value == null) {
            return Optional.empty();
        }
        if (value instanceof Map) {
            return Optional.of(new MapConfiguration((Map<String, Object>) value));
        }
        return Optional.empty();
    }

    @Override
    public List<AppConfiguration> getConfigList(String key) {
        Object value = getRawValue(key);
        if (value == null) {
            return Collections.emptyList();
        }
        if (value instanceof List) {
            List<AppConfiguration> result = new ArrayList<>();
            for (Object item : (List<?>) value) {
                if (item instanceof Map) {
                    result.add(new MapConfiguration((Map<String, Object>) item));
                }
            }
            return result;
        }
        return Collections.emptyList();
    }

    @Override
    public Map<String, Object> toMap() {
        Map<String, Object> result = new LinkedHashMap<>();
        for (String key : getKeys()) {
            Object value = getRawValue(key);
            if (value instanceof AppConfiguration) {
                result.put(key, ((AppConfiguration) value).toMap());
            } else if (value instanceof List) {
                List<Object> converted = new ArrayList<>();
                for (Object item : (List<?>) value) {
                    if (item instanceof AppConfiguration) {
                        converted.add(((AppConfiguration) item).toMap());
                    } else {
                        converted.add(item);
                    }
                }
                result.put(key, converted);
            } else {
                result.put(key, value);
            }
        }
        return result;
    }

    @Override
    public ConfigValueType getValueType(String key) {
        if (!hasKey(key)) {
            return ConfigValueType.UNKNOWN;
        }
        Object value = getRawValue(key);
        if (value == null) {
            return ConfigValueType.NULL;
        } else if (value instanceof String) {
            return ConfigValueType.STRING;
        } else if (value instanceof Number) {
            return ConfigValueType.NUMBER;
        } else if (value instanceof Boolean) {
            return ConfigValueType.BOOLEAN;
        } else if (value instanceof List) {
            return ConfigValueType.LIST;
        } else if (value instanceof Map || value instanceof AppConfiguration) {
            return ConfigValueType.MAP;
        }
        return ConfigValueType.UNKNOWN;
    }

    protected boolean hasKey(String key) {
        throw new UnsupportedOperationException("hasKey not implemented");
    }

    protected abstract <T> Optional<T> getValue(String key, Class<T> type);
}
