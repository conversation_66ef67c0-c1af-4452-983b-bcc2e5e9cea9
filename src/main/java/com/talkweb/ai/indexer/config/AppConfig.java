package com.talkweb.ai.indexer.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import java.io.InputStream;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import com.talkweb.ai.indexer.cli.SpringFactory;
import org.springframework.context.ApplicationContext;
import picocli.CommandLine;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * Application configuration manager
 */
@Configuration
public class AppConfig {
    private static final Logger log = LoggerFactory.getLogger(AppConfig.class);

    @Value("${doc-converter.plugins.directory:plugins}")
    private String pluginsDir;

    @Value("${doc-converter.temp-dir:temp}")
    private String tempDir;

    @Value("${doc-converter.logging.file:logs/application.log}")
    private String logFile;

    @Value("${spring.config.additional-location:}")
    private String additionalConfigLocation;

    /**
     * Creates and configures the main application configuration
     */
    @Bean
    public AppConfiguration appConfiguration() {
        List<AppConfiguration> configs = new ArrayList<>();

        // 1. Load default configuration from classpath
        configs.add(loadClasspathConfig("config/application.yaml"));

        // 2. Load additional configuration if specified
        if (additionalConfigLocation != null && !additionalConfigLocation.isEmpty()) {
            Path configPath = Paths.get(additionalConfigLocation);
            if (Files.exists(configPath)) {
                try {
                    AppConfiguration externalConfig = Configurations.load(configPath);
                    configs.add(externalConfig);
                    log.info("Loaded external configuration from: {}", configPath.toAbsolutePath());
                } catch (Exception e) {
                    log.error("Failed to load external configuration from: " + configPath, e);
                }
            }
        }

        // 3. Merge all configurations (last one takes precedence)
        AppConfiguration mergedConfig = mergeConfigurations(configs);

        // 4. Ensure required directories exist
        ensureDirectories(mergedConfig);

        return mergedConfig;
    }

    /**
     * Loads a configuration file from the classpath
     */
    private AppConfiguration loadClasspathConfig(String path) {
        try {
            Resource resource = new ClassPathResource(path);
            if (resource.exists()) {
                try (InputStream is = resource.getInputStream()) {
                    if (path.endsWith(".yaml") || path.endsWith(".yml")) {
                        return new YamlConfigurationLoader().load(is);
                    } else if (path.endsWith(".json")) {
                        return new JsonConfigurationLoader().load(is);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Failed to load configuration from classpath: " + path, e);
        }
        return new MapConfiguration();
    }

    /**
     * Merges multiple configurations into one
     */
    private AppConfiguration mergeConfigurations(List<AppConfiguration> configs) {
        MapConfiguration result = new MapConfiguration();

        for (AppConfiguration config : configs) {
            if (config != null) {
                for (String key : config.getKeys()) {
                    result.set(key, config.getRawValue(key));
                }
            }
        }

        return result;
    }

    /**
     * Ensures that required directories exist
     */
    private void ensureDirectories(AppConfiguration config) {
        try {
            // Ensure plugins directory exists
            String pluginsDir = config.getString("doc-converter.plugins.directory", "plugins");
            Files.createDirectories(Paths.get(pluginsDir));

            // Ensure temp directory exists
            String tempDir = config.getString("doc-converter.temp-dir", "temp");
            Files.createDirectories(Paths.get(tempDir));

            // Ensure log directory exists
            String logFile = config.getString("doc-converter.logging.file", "logs/application.log");
            Path logDir = Paths.get(logFile).getParent();
            if (logDir != null) {
                Files.createDirectories(logDir);
            }

            log.info("Using plugins directory: {}", Paths.get(pluginsDir).toAbsolutePath());
            log.info("Using temp directory: {}", Paths.get(tempDir).toAbsolutePath());
            log.info("Log file: {}", Paths.get(logFile).toAbsolutePath());

        } catch (IOException e) {
            log.error("Failed to create required directories", e);
            throw new RuntimeException("Failed to initialize application directories", e);
        }
    }

    @Bean
    public Path pluginsDirectory() {
        return Paths.get(pluginsDir).toAbsolutePath();
    }

    @Bean
    public Path tempDirectory() {
        return Paths.get(tempDir).toAbsolutePath();
    }

    @Bean
    public Path logFilePath() {
        return Paths.get(logFile).toAbsolutePath();
    }

    @Bean
    public CommandLine.IFactory commandLineFactory(ApplicationContext applicationContext) {
        return new SpringFactory(applicationContext);
    }
}
